import React, { useState, useEffect } from 'react';
import './LandingPage.css';

const LandingPage = ({ onBookingClick }) => {
  const [currentPage, setCurrentPage] = useState('home');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isHeaderVisible, setIsHeaderVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // Scroll-Handler für Auto-Hide/Show Header
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        // Scrolling down & past 100px
        setIsHeaderVisible(false);
        setIsMobileMenuOpen(false); // Close mobile menu when scrolling down
      } else if (currentScrollY < lastScrollY) {
        // Scrolling up
        setIsHeaderVisible(true);
      }

      setLastScrollY(currentScrollY);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [lastScrollY]);

  const navigate = (page) => {
    // Handle external redirects
    if (page === 'medenspiele') {
      window.open('https://htv.liga.nu/cgi-bin/WebObjects/nuLigaTENDE.woa/wa/clubMeetings?club=24972', '_blank');
      return;
    }
    
    if (page === 'mitglied-werden') {
      window.open('http://tc-wehen.de/wp-content/uploads/2025/04/Beitragsordnung_TCWehen_2025.pdf', '_blank');
      return;
    }
    
    if (page === 'satzung') {
      window.open('https://tc-wehen.com/wp-content/uploads/2021/03/Satzung_TC-Wehen_Maerz_2020.pdf', '_blank');
      return;
    }
    
    if (page === 'hand-spanndienst') {
      window.open('http://tc-wehen.de/wp-content/uploads/2024/04/TCW_HSD_Arbeitsstunden.pdf', '_blank');
      return;
    }
    
    if (page === 'mach-mit') {
      window.open('http://tc-wehen.de/wp-content/uploads/2024/01/Mithilfe_Mitglieder.pdf', '_blank');
      return;
    }

    if (page === 'buchung') {
      onBookingClick();
      return;
    }

    setCurrentPage(page);
    setIsMobileMenuOpen(false); // Close mobile menu when navigating
    setIsDropdownOpen(false); // Close dropdown when navigating
  };

  const toggleDropdown = (e) => {
    e.preventDefault();
    setIsDropdownOpen(!isDropdownOpen);
  };

  const Header = () => (
    <header className={`header ${isHeaderVisible ? 'header-visible' : 'header-hidden'}`}>
      <div className="header-content">
        <div className="logo">
          <img src="/assets/Logo-TCW.PNG" alt="TC-Wehen Logo" className="logo-img" />
          <h1>TC-Wehen</h1>
        </div>

        {/* Desktop Navigation */}
        <nav className="nav desktop-nav">
          <ul className="nav-list">
            <li><a href="#" onClick={() => navigate('home')} className={currentPage === 'home' ? 'active' : ''}>Home</a></li>
            <li><a href="#" onClick={() => navigate('training')} className={currentPage === 'training' ? 'active' : ''}>Training</a></li>
            <li><a href="#" onClick={() => navigate('buchung')} className="booking-button">Platz buchen</a></li>
            <li className={`dropdown ${isDropdownOpen ? 'dropdown-open' : ''}`}>
              <a href="#" onClick={toggleDropdown} className={currentPage === 'verein' ? 'active' : ''}>
                Verein <span className="dropdown-arrow">▼</span>
              </a>
              <ul className="dropdown-menu">
                <li><a href="#" onClick={() => navigate('verein')}>Verein Info</a></li>
                <li><a href="#" onClick={() => navigate('verein-anlage')}>Unsere Anlage</a></li>
                <li><a href="#" onClick={() => navigate('mitglied-werden')}>Mitglied werden</a></li>
                <li><a href="#" onClick={() => navigate('satzung')}>Satzung</a></li>
                <li><a href="#" onClick={() => navigate('hand-spanndienst')}>Hand- und Spanndienst</a></li>
                <li><a href="#" onClick={() => navigate('mach-mit')}>Mach Mit!</a></li>
              </ul>
            </li>
            <li><a href="#" onClick={() => navigate('medenspiele')} className={currentPage === 'medenspiele' ? 'active' : ''}>Medenspiele</a></li>
            <li><a href="#" onClick={() => navigate('kontakt')} className={currentPage === 'kontakt' ? 'active' : ''}>Kontakt</a></li>
          </ul>
        </nav>

        {/* Hamburger Menu Button - Only visible on mobile */}
        <button
          className="mobile-menu-toggle"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          aria-label="Menu"
        >
          <span className={`hamburger-line ${isMobileMenuOpen ? 'open' : ''}`}></span>
          <span className={`hamburger-line ${isMobileMenuOpen ? 'open' : ''}`}></span>
          <span className={`hamburger-line ${isMobileMenuOpen ? 'open' : ''}`}></span>
        </button>

        {/* Mobile Navigation - Only visible when menu is open */}
        <nav className={`nav mobile-nav ${isMobileMenuOpen ? 'nav-open' : ''}`}>
          <ul className="nav-list">
            <li><a href="#" onClick={() => navigate('home')} className={currentPage === 'home' ? 'active' : ''}>Home</a></li>
            <li><a href="#" onClick={() => navigate('training')} className={currentPage === 'training' ? 'active' : ''}>Training</a></li>
            <li><a href="#" onClick={() => navigate('buchung')} className="booking-button">Platz buchen</a></li>
            <li className={`dropdown ${isDropdownOpen ? 'dropdown-open' : ''}`}>
              <a href="#" onClick={toggleDropdown} className={currentPage === 'verein' ? 'active' : ''}>
                Verein <span className="dropdown-arrow">▼</span>
              </a>
              <ul className="dropdown-menu">
                <li><a href="#" onClick={() => navigate('verein')}>Verein Info</a></li>
                <li><a href="#" onClick={() => navigate('verein-anlage')}>Unsere Anlage</a></li>
                <li><a href="#" onClick={() => navigate('mitglied-werden')}>Mitglied werden</a></li>
                <li><a href="#" onClick={() => navigate('satzung')}>Satzung</a></li>
                <li><a href="#" onClick={() => navigate('hand-spanndienst')}>Hand- und Spanndienst</a></li>
                <li><a href="#" onClick={() => navigate('mach-mit')}>Mach Mit!</a></li>
              </ul>
            </li>
            <li><a href="#" onClick={() => navigate('medenspiele')} className={currentPage === 'medenspiele' ? 'active' : ''}>Medenspiele</a></li>
            <li><a href="#" onClick={() => navigate('kontakt')} className={currentPage === 'kontakt' ? 'active' : ''}>Kontakt</a></li>
          </ul>
        </nav>
      </div>
    </header>
  );

  const HomePage = () => (
    <div className="page home-page" style={{backgroundImage: "url('/assets/Plätze.PNG')"}}>
      <div className="home-content">
        <div className="welcome-message">
          <h2>Ihr Tennisverein in Wehen</h2>
          <p className="welcome-subtitle">Unser Tennisclub liegt herrlich in Wehen mit 6 gepflegten Sandplätzen und einem gemütlichen Vereinsheim.</p>
        </div>

        <div className="news-section">
          <h3>Aktuelle News</h3>
          <div className="news-grid">
            <div className="news-item">
              <div className="news-date">15. Dez 2024</div>
              <h4>Winterpause beendet</h4>
              <p>Ab sofort sind unsere Plätze wieder geöffnet! Wir freuen uns auf die neue Saison.</p>
            </div>
            <div className="news-item">
              <div className="news-date">10. Dez 2024</div>
              <h4>Neue Buchungszeiten</h4>
              <p>Das Online-Buchungssystem ist jetzt verfügbar. Buchen Sie Ihren Platz bequem von zu Hause.</p>
            </div>
            <div className="news-item">
              <div className="news-date">5. Dez 2024</div>
              <h4>Tennisschule Prätorius</h4>
              <p>Professionelles Training für alle Altersgruppen. Kontaktieren Sie Frank Prätorius für weitere Informationen.</p>
            </div>
          </div>
        </div>

        {/* Teaser Sections */}
        <div className="teasers-section">
          <h3>Entdecken Sie den TC-Wehen</h3>
          <div className="teasers-grid">

            {/* Training Teaser */}
            <div className="teaser-card">
              <div className="teaser-icon">🎾</div>
              <h4>Professionelles Training</h4>
              <p>
                Von Kindern bis Erwachsene - Frank Prätorius bietet individuelles Training
                für alle Spielstärken. Schnupperstunden möglich!
              </p>
              <button className="teaser-button" onClick={() => navigate('training')}>
                Mehr zum Training →
              </button>
            </div>

            {/* Verein Teaser */}
            <div className="teaser-card">
              <div className="teaser-icon">🏆</div>
              <h4>Unser Verein</h4>
              <p>
                Seit 1978 ein familienfreundlicher Tennisverein mit über 120 Mitgliedern.
                Gemeinschaft und sportliche Fairness stehen bei uns im Mittelpunkt.
              </p>
              <button className="teaser-button" onClick={() => navigate('verein')}>
                Verein kennenlernen →
              </button>
            </div>

            {/* Anlage Teaser */}
            <div className="teaser-card">
              <div className="teaser-icon">🌟</div>
              <h4>Unsere Anlage</h4>
              <p>
                6 gepflegte Sandplätze, eine Ballwand und ein gemütliches Vereinsheim
                mit Terrasse - perfekt für entspannte Stunden nach dem Spiel.
              </p>
              <button className="teaser-button" onClick={() => navigate('verein-anlage')}>
                Anlage entdecken →
              </button>
            </div>

            {/* Kontakt Teaser */}
            <div className="teaser-card">
              <div className="teaser-icon">📞</div>
              <h4>Kontakt & Öffnungszeiten</h4>
              <p>
                Haben Sie Fragen oder möchten uns besuchen? Hier finden Sie alle
                Kontaktdaten und Öffnungszeiten unserer Anlage.
              </p>
              <button className="teaser-button" onClick={() => navigate('kontakt')}>
                Kontakt aufnehmen →
              </button>
            </div>

            {/* Medenspiele Teaser */}
            <div className="teaser-card">
              <div className="teaser-icon">🏅</div>
              <h4>Medenspiele</h4>
              <p>
                Verfolgen Sie die Ergebnisse unserer Mannschaften in den aktuellen
                Medenrunden. Spannende Matches und tolle Erfolge!
              </p>
              <button className="teaser-button" onClick={() => navigate('medenspiele')}>
                Zu den Ergebnissen →
              </button>
            </div>

            {/* Platz buchen Teaser */}
            <div className="teaser-card teaser-highlight">
              <div className="teaser-icon">📅</div>
              <h4>Platz buchen</h4>
              <p>
                Buchen Sie Ihren Tennisplatz bequem online! Unser Buchungssystem
                ist rund um die Uhr verfügbar - einfach und schnell.
              </p>
              <button className="teaser-button teaser-button-primary" onClick={() => navigate('buchung')}>
                Jetzt buchen →
              </button>
            </div>

          </div>
        </div>
      </div>
    </div>
  );

  const TrainingPage = () => (
    <div className="page training-page" style={{backgroundImage: "url('/assets/Plätze3.PNG')"}}>
      <div className="content">
        <div className="back-button-container">
          <button className="back-button" onClick={() => navigate('home')}>
            ← Zurück zur Startseite
          </button>
        </div>
        <div className="training-content">
          <img src="/assets/Tennisschule-Prätorius.PNG" alt="Tennisschule Prätorius" className="training-logo" />

          {/* Trainer Vorstellung */}
          <div className="trainer-section">
            <div className="trainer-placeholder">
              <div className="trainer-image-placeholder">
                [Platzhalter für Bild von Frank Prätorius]
              </div>
              <div className="trainer-info">
                <h3>Frank Prätorius</h3>
                <p className="trainer-title">Lizenzierter Tennistrainer & Vereinstrainer</p>
                <p className="trainer-description">
                  Mit über 15 Jahren Erfahrung im Tennissport biete ich professionelles Training
                  für alle Altersgruppen und Spielstärken. Meine Philosophie: Tennis soll Spaß machen
                  und gleichzeitig technisch fundiert vermittelt werden.
                </p>
              </div>
            </div>
          </div>

          {/* Trainingsangebot */}
          <div className="training-programs">
            <h3>Unser Trainingsangebot</h3>

            <div className="program-grid">
              <div className="program-card">
                <h4>🎾 Kindertraining (5-12 Jahre)</h4>
                <ul>
                  <li>Spielerischer Einstieg in den Tennissport</li>
                  <li>Koordination und Ballgefühl entwickeln</li>
                  <li>Kleingruppen (max. 4 Kinder)</li>
                  <li>Altersgerechte Schläger und Bälle</li>
                </ul>

              </div>

              <div className="program-card">
                <h4>🏆 Jugendtraining (13-18 Jahre)</h4>
                <ul>
                  <li>Technikverfeinerung und Taktikschulung</li>
                  <li>Wettkampfvorbereitung</li>
                  <li>Konditions- und Krafttraining</li>
                  <li>Turnierbegleitung</li>
                </ul>

              </div>

              <div className="program-card">
                <h4>👥 Erwachsenentraining</h4>
                <ul>
                  <li>Anfänger: Grundschläge erlernen</li>
                  <li>Fortgeschrittene: Technikoptimierung</li>
                  <li>Spielpraxis und Matchtraining</li>
                  <li>Flexible Terminvereinbarung</li>
                </ul>

              </div>

              <div className="program-card">
                <h4>🎯 Einzeltraining</h4>
                <ul>
                  <li>Individuelle Betreuung</li>
                  <li>Gezieltes Techniktraining</li>
                  <li>Schnelle Fortschritte</li>
                  <li>Flexible Termingestaltung</li>
                </ul>

              </div>
            </div>
          </div>

          {/* Preise und Kontakt */}
          <div className="training-info-section">
            <div className="pricing-contact">
              <div className="pricing-info">
                <h4>Preise (Beispiel)</h4>
                <ul className="price-list">
                  <li>Einzelstunde (60 Min): 45€</li>
                  <li>Gruppentraining (4er): 20€/Person</li>
                  <li>Kindertraining (45 Min): 15€</li>
                  <li>10er-Karte Einzeltraining: 400€</li>
                </ul>
                <p className="price-note">*Preise für Vereinsmitglieder. Gäste zahlen einen Aufschlag.</p>
              </div>

              <div className="contact-info">
                <h4>Training buchen</h4>
                <div className="contact-details">
                  <p><strong>📞 Telefon:</strong> [Telefonnummer Frank Prätorius]</p>
                  <p><strong>📧 Email:</strong> <EMAIL></p>
                  <p><strong>📱 WhatsApp:</strong> [Handynummer]</p>
                  <p><strong>🕐 Erreichbarkeit:</strong> Mo-Fr 16:00-20:00</p>
                </div>
                <div className="booking-note">
                  <p>Schnupperstunden für Anfänger möglich! Einfach anrufen und Termin vereinbaren.</p>
                </div>
              </div>
            </div>
          </div>

          {/* Trainingsphilosophie */}
          <div className="philosophy-section">
            <h4>Unsere Trainingsphilosophie</h4>
            <div className="philosophy-content">
              <p>
                <strong>🎯 Individuell:</strong> Jeder Spieler wird dort abgeholt, wo er steht.
                Egal ob Anfänger oder Fortgeschrittener - das Training wird an die persönlichen
                Bedürfnisse angepasst.
              </p>
              <p>
                <strong>🏃‍♂️ Ganzheitlich:</strong> Neben der Technik stehen auch Taktik,
                Kondition und mentale Stärke im Fokus. Tennis ist mehr als nur Schläge!
              </p>
              <p>
                <strong>😊 Mit Spaß:</strong> Tennis soll Freude bereiten! Durch abwechslungsreiche
                Übungen und positive Motivation schaffen wir eine Atmosphäre, in der Lernen Spaß macht.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const VereinPage = () => (
    <div className="page verein-page" style={{backgroundImage: "url('/assets/Plätze2.PNG')"}}>
      <div className="content">
        <div className="back-button-container">
          <button className="back-button" onClick={() => navigate('home')}>
            ← Zurück zur Startseite
          </button>
        </div>
        <div className="verein-content">
          <h2>Unser Verein</h2>

          {/* Vereinsgeschichte */}
          <div className="verein-section">
            <h3>Über den TC-Wehen</h3>
            <p>
              Der Tennis-Club Wehen e.V. wurde 1978 gegründet und ist seitdem ein fester Bestandteil
              der Gemeinde Wehen. Mit über 40 Jahren Vereinsgeschichte haben wir uns zu einem
              modernen und familienfreundlichen Tennisverein entwickelt.
            </p>
            <p>
              Unser Verein steht für Gemeinschaft, sportliche Fairness und die Förderung des
              Tennissports für alle Altersgruppen. Von Kindern bis zu Senioren - bei uns ist
              jeder willkommen!
            </p>
          </div>

          {/* Vereinsleben */}
          <div className="verein-section">
            <h3>Vereinsleben</h3>
            <div className="verein-highlights">
              <div className="highlight-item">
                <h4>🎾 Aktive Mitglieder</h4>
                <p>Über 120 Mitglieder aller Altersgruppen spielen regelmäßig auf unseren Plätzen</p>
              </div>
              <div className="highlight-item">
                <h4>🏆 Mannschaften</h4>
                <p>Mehrere Herren-, Damen- und Jugendmannschaften vertreten uns in den Medenrunden</p>
              </div>
              <div className="highlight-item">
                <h4>🎉 Veranstaltungen</h4>
                <p>Vereinsturniere, Sommerfest und gesellige Abende stärken unsere Gemeinschaft</p>
              </div>
              <div className="highlight-item">
                <h4>👨‍👩‍👧‍👦 Familienfreundlich</h4>
                <p>Spezielle Angebote für Familien und Kinder machen Tennis für alle zugänglich</p>
              </div>
            </div>
          </div>

          {/* Vorstand */}
          <div className="verein-section">
            <h3>Unser Vorstand</h3>
            <div className="vorstand-placeholder">
              [Platzhalter für Gruppenfoto des Vorstandes]
            </div>
            <div className="vorstand-liste">
              <p>Unser engagierter Vorstand sorgt für die professionelle Führung des Vereins:</p>
              <ul>
                <li><strong>1. Vorsitzender:</strong> [Name] - Vereinsführung und Repräsentation</li>
                <li><strong>2. Vorsitzender:</strong> [Name] - Stellvertretung und Projekte</li>
                <li><strong>Kassenwart:</strong> [Name] - Finanzen und Buchhaltung</li>
                <li><strong>Schriftführer:</strong> [Name] - Protokolle und Korrespondenz</li>
                <li><strong>Sportwart:</strong> [Name] - Sportbetrieb und Turniere</li>
                <li><strong>Jugendwart:</strong> [Name] - Jugendförderung und Nachwuchsarbeit</li>
                <li><strong>Platzwart:</strong> [Name] - Anlagenpflege und Instandhaltung</li>
              </ul>
            </div>
          </div>

          {/* Mitgliedschaft */}
          <div className="verein-section">
            <h3>Mitglied werden</h3>
            <p>
              Interessiert an einer Mitgliedschaft? Wir freuen uns über neue Gesichter!
              Als Mitglied genießen Sie viele Vorteile:
            </p>
            <ul className="membership-benefits">
              <li>Kostenlose Platznutzung während der Öffnungszeiten</li>
              <li>Teilnahme an Vereinsturnieren und Medenrunden</li>
              <li>Vergünstigte Trainerstunden</li>
              <li>Gesellige Vereinsveranstaltungen</li>
              <li>Nutzung des Vereinsheims und der Terrasse</li>
            </ul>
            <p>
              <strong>Schnuppern erwünscht!</strong> Kommen Sie einfach vorbei und lernen Sie
              unseren Verein kennen. Gerne können Sie auch ein Probetraining vereinbaren.
            </p>
          </div>
        </div>
      </div>
    </div>
  );

  const AnlagePage = () => (
    <div className="page anlage-page" style={{backgroundImage: "url('/assets/Obenansicht-Plätze.PNG')"}}>
      <div className="content">
        <div className="back-button-container">
          <button className="back-button" onClick={() => navigate('home')}>
            ← Zurück zur Startseite
          </button>
        </div>
        <div className="anlage-content">
          <h2>Unsere Anlage</h2>

          {/* Hero Image - Obenansicht als Hauptbild */}
          <div className="hero-image">
            <img src="/assets/Obenansicht-Plätze.PNG" alt="Luftaufnahme unserer Tennisanlage" />
          </div>

          {/* Bildergalerie in strukturierter Anordnung */}
          <div className="anlage-images">
            <div className="main-images">
              <img src="/assets/Plätze.PNG" alt="Tennisplätze Hauptansicht" />
              <img src="/assets/Plätze2.PNG" alt="Tennisplätze Seitenansicht" />
            </div>
            <div className="secondary-images">
              <img src="/assets/Plätze3.PNG" alt="Tennisplätze Detailansicht" />
              <img src="/assets/Terrassenansicht-Plätze.PNG" alt="Blick von der Terrasse" />
            </div>
          </div>

          <p>Hier beim TC-Wehen haben wir 6 schöne und gepflegte Sandplätze und eine Ballwand. Sonne lässt sich bis zum Abend wunderschön auf unserer Terrasse bewundern! Kommt doch mal vorbei!</p>

          {/* Anfahrt Information */}
          <div className="location-info">
            <h3>Anfahrt</h3>
            <div className="address-card">
              <h4>Tennis-Club Wehen e.V.</h4>
              <p className="address-line">Platter Straße 89</p>
              <p className="address-line">65232 Taunusstein</p>
            </div>
          </div>

          <div className="google-maps">
            <iframe
              src="https://maps.google.com/maps?q=50.151,8.207+(TC-Wehen+e.V.+Tennisclub)&t=&z=16&ie=UTF8&iwloc=&output=embed"
              width="100%"
              height="300"
              style={{border: 0, borderRadius: '8px'}}
              allowFullScreen=""
              loading="lazy"
              referrerPolicy="no-referrer-when-downgrade"
              title="TC-Wehen e.V. - Tennisclub in Wehen, Taunusstein"
            ></iframe>
            <div className="map-info">
              <p className="map-description">
                📍 TC-Wehen Tennisclub in Wehen, Taunusstein
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const KontaktPage = () => (
    <div className="page kontakt-page" style={{backgroundImage: "url('/assets/Ball.PNG')"}}>
      <div className="content">
        <div className="back-button-container">
          <button className="back-button" onClick={() => navigate('home')}>
            ← Zurück zur Startseite
          </button>
        </div>
        <div className="kontakt-content">
          <h2>Kontakt</h2>

          {/* Hauptkontakt */}
          <div className="contact-section">
            <h3>Vereinskontakt</h3>
            <div className="contact-cards">
              <div className="contact-card">
                <h4>📞 Telefon</h4>
                <p><strong>[Platzhalter Telefonnummer]</strong></p>
                <p>Erreichbarkeit: Mo-Fr 18:00-20:00</p>
              </div>
              <div className="contact-card">
                <h4>📧 E-Mail</h4>
                <p><strong>[Platzhalter Club Email]</strong></p>
                <p>Wir antworten innerhalb von 24 Stunden</p>
              </div>
              <div className="contact-card">
                <h4>📍 Adresse</h4>
                <p><strong>Tennis-Club Wehen e.V.</strong></p>
                <p>Platter Straße 89</p>
                <p>65232 Taunusstein</p>
              </div>
            </div>
          </div>

          {/* Ansprechpartner */}
          <div className="contact-section">
            <h3>Ihre Ansprechpartner</h3>
            <div className="contact-persons">
              <div className="person-card">
                <h4>🏆 Vereinsführung</h4>
                <p><strong>1. Vorsitzender: [Name]</strong></p>
                <p>📞 [Telefonnummer]</p>
                <p>📧 [E-Mail]</p>
                <p><em>Allgemeine Vereinsangelegenheiten, Mitgliedschaft</em></p>
              </div>
              <div className="person-card">
                <h4>🎾 Sportbetrieb</h4>
                <p><strong>Sportwart: [Name]</strong></p>
                <p>📞 [Telefonnummer]</p>
                <p>📧 [E-Mail]</p>
                <p><em>Turniere, Medenrunden, Sportveranstaltungen</em></p>
              </div>
              <div className="person-card">
                <h4>👨‍🏫 Training</h4>
                <p><strong>Frank Prätorius</strong></p>
                <p>📞 [Telefonnummer Frank Prätorius]</p>
                <p>📧 <EMAIL></p>
                <p><em>Tennistraining, Schnupperstunden</em></p>
              </div>
              <div className="person-card">
                <h4>🔧 Anlage & Technik</h4>
                <p><strong>Platzwart: [Name]</strong></p>
                <p>📞 [Telefonnummer]</p>
                <p>📧 [E-Mail]</p>
                <p><em>Platzpflege, technische Probleme</em></p>
              </div>
            </div>
          </div>

          {/* Öffnungszeiten */}
          <div className="contact-section">
            <h3>Öffnungszeiten</h3>
            <div className="opening-hours">
              <div className="hours-card">
                <h4>🌞 Sommersaison (April - Oktober)</h4>
                <ul>
                  <li><strong>Montag - Freitag:</strong> 08:00 - 22:00 Uhr</li>
                  <li><strong>Samstag - Sonntag:</strong> 08:00 - 22:00 Uhr</li>
                  <li><strong>Feiertage:</strong> 08:00 - 22:00 Uhr</li>
                </ul>
                <p><em>Plätze sind bei Tageslicht bis 22:00 Uhr bespielbar</em></p>
              </div>
              <div className="hours-card">
                <h4>❄️ Wintersaison (November - März)</h4>
                <ul>
                  <li><strong>Montag - Freitag:</strong> 09:00 - 18:00 Uhr</li>
                  <li><strong>Samstag - Sonntag:</strong> 09:00 - 18:00 Uhr</li>
                </ul>
                <p><em>Witterungsabhängig - bei Frost geschlossen</em></p>
              </div>
            </div>
          </div>

          {/* Anfahrt */}
          <div className="contact-section">
            <h3>Anfahrt</h3>
            <div className="directions">
              <div className="direction-item">
                <h4>🚗 Mit dem Auto</h4>
                <p>A3 Ausfahrt Wiesbaden-Erbenheim, dann B417 Richtung Taunusstein-Wehen.
                   In Wehen der Platter Straße folgen. Kostenlose Parkplätze direkt am Vereinsgelände.</p>
              </div>
              <div className="direction-item">
                <h4>🚌 Mit öffentlichen Verkehrsmitteln</h4>
                <p>Bus Linie 274 bis Haltestelle "Wehen Ortsmitte", dann 5 Minuten Fußweg
                   über die Platter Straße zum Tennisclub.</p>
              </div>
              <div className="direction-item">
                <h4>🚶‍♂️ Zu Fuß</h4>
                <p>Vom Ortskern Wehen sind es nur wenige Gehminuten über die Platter Straße.
                   Der Tennisclub ist gut ausgeschildert.</p>
              </div>
            </div>
          </div>

          {/* Kontaktformular Hinweis */}
          <div className="contact-section">
            <div className="contact-note">
              <h4>💬 Haben Sie Fragen?</h4>
              <p>
                Zögern Sie nicht, uns zu kontaktieren! Ob Interesse an einer Mitgliedschaft,
                Fragen zum Training oder einfach nur Neugier auf unseren Verein - wir freuen
                uns über jede Nachricht.
              </p>
              <p>
                <strong>Tipp:</strong> Kommen Sie doch einfach mal vorbei! Während der
                Öffnungszeiten ist immer jemand da, der Ihnen gerne weiterhilft.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const ImpressumPage = () => (
    <div className="page impressum-page" style={{backgroundImage: "url('/assets/Plätze.PNG')"}}>
      <div className="content">
        <div className="back-button-container">
          <button className="back-button" onClick={() => navigate('home')}>
            ← Zurück zur Startseite
          </button>
        </div>
        <div className="impressum-content">
          <h2>Impressum</h2>
          <div className="impressum-info">
            <h3>Angaben gemäß § 5 TMG</h3>
            <p><strong>TC-Wehen e.V.</strong></p>
            <p>[Vereinsadresse]</p>
            <p>[PLZ Ort]</p>

            <h3>Vertreten durch:</h3>
            <p>1. Vorsitzender: [Name]</p>
            <p>2. Vorsitzender: [Name]</p>

            <h3>Kontakt:</h3>
            <p>Telefon: [Telefonnummer]</p>
            <p>E-Mail: [E-Mail-Adresse]</p>

            <h3>Registereintrag:</h3>
            <p>Eintragung im Vereinsregister</p>
            <p>Registergericht: [Amtsgericht]</p>
            <p>Registernummer: [VR-Nummer]</p>

            <h3>Verantwortlich für den Inhalt nach § 55 Abs. 2 RStV:</h3>
            <p>[Name des Verantwortlichen]</p>
            <p>[Adresse]</p>
          </div>
        </div>
      </div>
    </div>
  );

  const DatenschutzPage = () => (
    <div className="page datenschutz-page" style={{backgroundImage: "url('/assets/Terrassenansicht-Plätze.PNG')"}}>
      <div className="content">
        <div className="back-button-container">
          <button className="back-button" onClick={() => navigate('home')}>
            ← Zurück zur Startseite
          </button>
        </div>
        <div className="datenschutz-content">
          <h2>Datenschutzerklärung</h2>
          <div className="datenschutz-info">
            <h3>1. Datenschutz auf einen Blick</h3>
            <p>Die folgenden Hinweise geben einen einfachen Überblick darüber, was mit Ihren personenbezogenen Daten passiert, wenn Sie unsere Website besuchen.</p>

            <h3>2. Allgemeine Hinweise und Pflichtinformationen</h3>
            <h4>Datenschutz</h4>
            <p>Die Betreiber dieser Seiten nehmen den Schutz Ihrer persönlichen Daten sehr ernst. Wir behandeln Ihre personenbezogenen Daten vertraulich und entsprechend der gesetzlichen Datenschutzvorschriften sowie dieser Datenschutzerklärung.</p>

            <h3>3. Datenerfassung auf unserer Website</h3>
            <h4>Wer ist verantwortlich für die Datenerfassung auf dieser Website?</h4>
            <p>Die Datenverarbeitung auf dieser Website erfolgt durch den Websitebetreiber. Dessen Kontaktdaten können Sie dem Impressum dieser Website entnehmen.</p>

            <h4>Wie erfassen wir Ihre Daten?</h4>
            <p>Ihre Daten werden zum einen dadurch erhoben, dass Sie uns diese mitteilen. Hierbei kann es sich z.B. um Daten handeln, die Sie in ein Kontaktformular eingeben.</p>

            <h3>4. Buchungssystem</h3>
            <p>Für die Nutzung unseres Buchungssystems ist eine Registrierung erforderlich. Dabei werden folgende Daten erhoben:</p>
            <ul>
              <li>Name und Vorname</li>
              <li>E-Mail-Adresse</li>
              <li>Buchungsdaten (Datum, Uhrzeit, Platz)</li>
            </ul>
            <p>Diese Daten werden ausschließlich zur Verwaltung der Platzbuchungen verwendet.</p>

            <h3>5. Ihre Rechte</h3>
            <p>Sie haben jederzeit das Recht unentgeltlich Auskunft über Herkunft, Empfänger und Zweck Ihrer gespeicherten personenbezogenen Daten zu erhalten.</p>
          </div>
        </div>
      </div>
    </div>
  );

  const PlaceholderPage = ({ title }) => (
    <div className="page placeholder-page" style={{backgroundImage: "url('/assets/Plätze2.PNG')"}}>
      <div className="content">
        <div className="back-button-container">
          <button className="back-button" onClick={() => navigate('home')}>
            ← Zurück zur Startseite
          </button>
        </div>
        <div className="placeholder-content">
          <h2>{title}</h2>
          <div className="placeholder-info">
            <p>Diese Seite ist noch in Bearbeitung.</p>
            <p>Weitere Informationen folgen in Kürze.</p>
            <p>Bei Fragen wenden Sie sich gerne an uns über die <a href="#" onClick={() => navigate('kontakt')} style={{color: 'var(--primary-red)', textDecoration: 'underline'}}>Kontakt-Seite</a>.</p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderPage = () => {
    switch(currentPage) {
      case 'home':
        return <HomePage />;
      case 'training':
        return <TrainingPage />;
      case 'verein':
        return <VereinPage />;
      case 'verein-anlage':
        return <AnlagePage />;
      case 'mitglied-werden':
        return <PlaceholderPage title="Mitglied werden" />;
      case 'satzung':
        return <PlaceholderPage title="Satzung" />;
      case 'hand-spanndienst':
        return <PlaceholderPage title="Hand- und Spanndienst" />;
      case 'mach-mit':
        return <PlaceholderPage title="Mach Mit!" />;
      case 'kontakt':
        return <KontaktPage />;
      case 'impressum':
        return <ImpressumPage />;
      case 'datenschutz':
        return <DatenschutzPage />;
      default:
        return <HomePage />;
    }
  };

  const Footer = () => (
    <footer className="footer">
      <div className="footer-content">
        <div className="footer-links">
          <a href="#" onClick={() => navigate('impressum')}>Impressum</a>
          <span>|</span>
          <a href="#" onClick={() => navigate('datenschutz')}>Datenschutz</a>
          <span>|</span>
          <a href="#" onClick={() => navigate('kontakt')}>Kontakt</a>
        </div>
        <div className="footer-text">
          <p>&copy; 2024 TC-Wehen e.V. - Alle Rechte vorbehalten</p>
        </div>
      </div>
    </footer>
  );

  return (
    <div className="landing-page">
      <Header />
      <main className="main">
        {renderPage()}
      </main>

      {/* Mobile Floating Action Button for Quick Booking */}
      {currentPage !== 'buchung' && (
        <button
          className="floating-action-btn"
          onClick={() => navigate('buchung')}
          aria-label="Schnell buchen"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 19H5V8H19V19ZM7 10H9V12H7V10ZM11 10H13V12H11V10ZM15 10H17V12H15V10Z" fill="currentColor"/>
          </svg>
          <span className="fab-text">Buchen</span>
        </button>
      )}

      <Footer />
    </div>
  );
};

export default LandingPage;
